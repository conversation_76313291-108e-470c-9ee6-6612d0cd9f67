package com.gymguy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 会员卡信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("member_card")
@ApiModel(value="MemberCard对象", description="会员卡信息表")
public class MemberCard implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "卡ID")
    @TableId(value = "card_id", type = IdType.ASSIGN_UUID)
    private String cardId;

    @ApiModelProperty(value = "会员ID")
    private String memberId;

    @ApiModelProperty(value = "卡类型：1-次卡，2-月卡，3-季卡，4-年卡")
    private Integer cardType;

    @ApiModelProperty(value = "总次数（次卡专用）")
    private Integer totalUsage;

    @ApiModelProperty(value = "剩余次数（次卡专用）")
    private Integer remainingUsage;

    @ApiModelProperty(value = "会员卡开始日期")
    private LocalDate startTime;

    @ApiModelProperty(value = "会员卡结束日期")
    private LocalDate endTime;

    @ApiModelProperty(value = "会员卡状态：1-有效，2-过期，3-冻结，4-退卡")
    private Integer status;


}
