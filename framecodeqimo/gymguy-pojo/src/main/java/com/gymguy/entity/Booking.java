package com.gymguy.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 课程预约表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("booking")
@ApiModel(value="Booking对象", description="课程预约表")
public class Booking implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "预约ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "学员ID")
    private Integer memberId;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "预约时间")
    private LocalDateTime bookingTime;

    @ApiModelProperty(value = "状态:1=已预约,2=已出席,0=已取消,3=未到")
    private Integer status;

    @ApiModelProperty(value = "签到时间")
    private LocalDateTime checkinTime;


}
