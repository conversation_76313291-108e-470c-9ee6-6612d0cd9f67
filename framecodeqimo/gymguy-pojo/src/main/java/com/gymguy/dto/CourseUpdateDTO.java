package com.gymguy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseUpdateDTO {
    private Integer id;

    private String name;

    private Integer coachId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String schedule;

    private Integer duration;

    private Integer maxCapacity;

    private Integer bookedCount;

    private String location;

    private Integer status;
}
