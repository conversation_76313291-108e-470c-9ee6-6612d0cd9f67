package com.gymguy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CoursePageQueryDTO {
    private Integer page;
    private Integer pageSize;

    private String name;//课程名称

    private String coachName;//教练名称

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;//开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;//结束时间

    private Integer duration;//时长

    private Integer maxCapacity;//最大容量

    private String location;//教室/区域

    private Integer status;//状态:1=未开始,2=进行中,3=已完成,0=已取消
}
