package com.gymguy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseAddDTO {
    @ApiModelProperty(value = "课程名称")
    private String name;

    @ApiModelProperty(value = "授课教练ID")
    private Integer coachId;

    @ApiModelProperty(value = "上课时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime schedule;

    @ApiModelProperty(value = "时长(分钟)")
    private Integer duration;

    @ApiModelProperty(value = "最大容量")
    private Integer maxCapacity;

    @ApiModelProperty(value = "教室/区域")
    private String location;
}
