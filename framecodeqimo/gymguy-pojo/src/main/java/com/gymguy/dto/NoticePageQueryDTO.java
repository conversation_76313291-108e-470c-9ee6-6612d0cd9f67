package com.gymguy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> @Description 公告分页查询DTO
 * @Date 2025/6/16
 */
@Data
@ApiModel("公告分页查询DTO")
public class NoticePageQueryDTO {
    
    @ApiModelProperty(value = "公告标题")
    private String title;

    @ApiModelProperty(value = "公告类型:1-系统公告,2-活动公告,3-维护公告")
    private Integer type;

    @ApiModelProperty(value = "状态:1-发布,2-草稿,3-下线")
    private Integer status;

    @ApiModelProperty(value = "页码")
    private Integer page = 1;

    @ApiModelProperty(value = "每页记录数")
    private Integer pageSize = 10;
}
