package com.gymguy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR> @Description 添加器材DTO
 * @Date 2025/6/16
 */
@Data
@ApiModel("添加器材DTO")
public class EquipmentAddDTO {
    
    @ApiModelProperty(value = "器材名称")
    private String name;

    @ApiModelProperty(value = "器材类型")
    private String type;

    @ApiModelProperty(value = "品牌")
    private String brand;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "购买日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate purchaseDate;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "位置")
    private String location;
}
