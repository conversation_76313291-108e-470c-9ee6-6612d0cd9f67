package com.gymguy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AddMemberDTO {
    @ApiModelProperty(value = "会员姓名")
    private String name;

    @ApiModelProperty(value = "会员性别")
    private String gender;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "生日")
    private LocalDate birthday;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "头像URL")
    private String avatar;

    @ApiModelProperty(value = "会员密码")
    private String password;
}
