package com.gymguy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CoachUpdateDTO {

    private Integer id;

    @ApiModelProperty(value = "教练姓名")
    private String name;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "性别:1=男,2=女")
    private Integer gender;

    @ApiModelProperty(value = "专长领域")
    private String specialty;

    @ApiModelProperty(value = "教练认证")
    private String certification;

    @ApiModelProperty(value = "状态:1=在职,0=离职")
    private Integer status;
}
