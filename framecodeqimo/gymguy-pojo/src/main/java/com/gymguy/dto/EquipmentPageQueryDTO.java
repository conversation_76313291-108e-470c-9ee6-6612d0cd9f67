package com.gymguy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> @Description 器材分页查询DTO
 * @Date 2025/6/16
 */
@Data
@ApiModel("器材分页查询DTO")
public class EquipmentPageQueryDTO {
    
    @ApiModelProperty(value = "器材名称")
    private String name;

    @ApiModelProperty(value = "器材类型")
    private String type;

    @ApiModelProperty(value = "状态:1-正常,2-维修中,3-报废")
    private Integer status;

    @ApiModelProperty(value = "页码")
    private Integer page = 1;

    @ApiModelProperty(value = "每页记录数")
    private Integer pageSize = 10;
}
