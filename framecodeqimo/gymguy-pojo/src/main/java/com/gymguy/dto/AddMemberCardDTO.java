package com.gymguy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class AddMemberCardDTO {
    @ApiModelProperty(value = "会员ID")
    private String memberId;
    @ApiModelProperty(value = "会员卡类型：1-次卡，2-月卡，3-季卡，4-年卡")
    private Integer cardType;
    @ApiModelProperty(value = "总次数（次卡专用）")
    private Integer totalUsage;
    @ApiModelProperty(value = "会员卡开始日期")
    private LocalDate startTime;
}
