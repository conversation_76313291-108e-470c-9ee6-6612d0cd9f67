package com.gymguy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> @Description 添加公告DTO
 * @Date 2025/6/16
 */
@Data
@ApiModel("公告DTO")
public class NoticeAddDTO {

    @ApiModelProperty(value = "公告ID（编辑时需要）")
    private Integer noticeId;

    @ApiModelProperty(value = "公告标题")
    private String title;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "公告类型:1-系统公告,2-活动公告,3-维护公告")
    private Integer type;

    @ApiModelProperty(value = "是否置顶:0-否,1-是")
    private Integer isTop;
}
