package com.gymguy.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 添加员工DTO
 * @Date 2025/6/13 16:33
 */
@Data
public class EmployeeAddDTO {
    @ApiModelProperty(value = "员工姓名")
    private String name;

    @ApiModelProperty(value = "员工性别")
    private String gender;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "角色: admin-管理员,staff-前台,coach-教练,member-会员")
    private String role;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入职时间")
    private LocalDate hireDate;

    @ApiModelProperty(value = "薪资")
    private BigDecimal salary;

    @ApiModelProperty("用户名")
    private String username;

//    @ApiModelProperty("密码")
//    private String password;

    @ApiModelProperty("头像")
    private String avatar;
}
