package com.gymguy.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoachVO {
    private Integer id;

    private String name;

    private String phone;

    private Integer gender;

    private String specialty;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate hireDate;

    private String certification;
}
