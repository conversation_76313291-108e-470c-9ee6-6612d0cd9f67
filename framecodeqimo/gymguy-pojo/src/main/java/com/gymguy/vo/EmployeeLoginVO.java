package com.gymguy.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel("员工登录vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmployeeLoginVO {
    @ApiModelProperty("员工token")
    private String employeeToken;
    @ApiModelProperty("员工姓名")
    private String name;
    @ApiModelProperty("员工角色")
    private String role;
    @ApiModelProperty("员工头像")
    private String avatar;
}
