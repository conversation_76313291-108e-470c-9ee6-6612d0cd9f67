package com.gymguy.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CourseVO {

    private Integer id;

    private String name;

    private Integer coachId;

    private String coachName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime schedule;

    private Integer duration;

    private Integer maxCapacity;

    private Integer bookedCount;

    private String location;

    private Integer status;
}
