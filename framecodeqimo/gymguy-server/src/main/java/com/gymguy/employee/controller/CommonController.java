package com.gymguy.employee.controller;

import com.gymguy.constant.MessageConstant;
import com.gymguy.result.Result;
import com.gymguy.utils.AliOssUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Description 通用接口
 * @Date 2025/6/13 10:07
 */
@Api(tags = "通用接口")
@RestController
@RequestMapping("/admin/common")
@Slf4j
public class CommonController {

    @Autowired
    private AliOssUtil aliOssUtil;

    @PostMapping("/upload")
    @ApiOperation("文件上传")
    public Result<String> upload(MultipartFile file){
        log.info("文件上传:{}",file);
        try {
            //获取源文件名
            String originalFilename = file.getOriginalFilename();
            //根据.截取后缀名
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String objectName = UUID.randomUUID().toString() + extension;
            //文件的请求路径
            String filepath = aliOssUtil.upload(file.getBytes(), objectName);
            return Result.success(filepath);
        } catch (IOException e) {
            log.info("文件上传失败:{}",e);
        }

        return Result.error(MessageConstant.UPLOAD_FAILED);
    }

    @DeleteMapping("/delete")
    @ApiOperation("文件删除")
    public Result<String> delete(@RequestParam("fileName") String fileName){
        log.info("文件删除:{}",fileName);
        // 需要从文件访问路径获取到文件名
        String objectName = fileName.substring(fileName.lastIndexOf("/") + 1);
        try {
            aliOssUtil.delete(objectName);
            return Result.success();
        } catch (Exception e) {
            log.info("文件删除失败:{}",e);
        }
        return Result.error(MessageConstant.DELETE_FAILED);
    }

}
