package com.gymguy.employee.service;

import com.gymguy.dto.EmployeeAddDTO;
import com.gymguy.dto.EmployeeLoginDTO;
import com.gymguy.dto.EmployeePageQueryDTO;
import com.gymguy.dto.ResetPasswordDTO;
import com.gymguy.dto.ResetPasswordDTO;
import com.gymguy.entity.Employee;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.EmployeeLoginVO;

/**
 * <p>
 * 员工信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface EmployeeService extends IService<Employee> {

    Employee findById(String id);

    /**
     * 员工登录
     * @param employeeLoginDTO
     * @return
     */
    EmployeeLoginVO login(EmployeeLoginDTO employeeLoginDTO);

    /**
     * 查询员工
     * @param dto
     * @return
     */
    PageResult  queryList(EmployeePageQueryDTO dto);

    /**
     * 新增员工
     * @param dto
     */
    Result add(EmployeeAddDTO dto);


    /**
     * 删除员工
     * <AUTHOR>
     * @Date 2025/6/17 9:05
     * @param id
     * @return
     */
    Result deleteById(String id);

    /**
     * 修改员工
     * <AUTHOR>
     * @Date 2025/6/17 10:48
     * @param employee
     * @return
     */
    Result updateEmp(Employee employee);

    /**
     * 重置员工密码
     * @param dto
     * @return
     */
    Result resetPassword(ResetPasswordDTO dto);
}
