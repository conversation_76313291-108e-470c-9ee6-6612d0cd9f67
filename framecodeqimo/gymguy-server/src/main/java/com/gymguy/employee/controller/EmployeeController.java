package com.gymguy.employee.controller;


import com.gymguy.dto.EmployeeAddDTO;
import com.gymguy.dto.EmployeeLoginDTO;
import com.gymguy.dto.EmployeePageQueryDTO;
import com.gymguy.dto.ResetPasswordDTO;
import com.gymguy.employee.service.EmployeeService;
import com.gymguy.entity.Employee;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.EmployeeLoginVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 员工信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Api(tags = "员工相关接口")
@RestController
@RequestMapping("/admin/employee")
@Slf4j
public class EmployeeController {
    @Autowired
    private EmployeeService employeeService;
    @GetMapping("findById/{id}")
    @ApiOperation("根据ID查询员工信息")
    public Result<Employee> findById(@PathVariable("id") String id){
        Employee employee=employeeService.findById(id);
        return Result.success(employee);
    }

    @PostMapping("login")
    @ApiOperation("员工登录")
    public Result<EmployeeLoginVO> login(@RequestBody EmployeeLoginDTO employeeLoginDTO){
        log.info("员工登录：{}",employeeLoginDTO);
        EmployeeLoginVO employeeLoginVO=employeeService.login(employeeLoginDTO);
        return Result.success(employeeLoginVO);
    }

    @PostMapping("/list")
    @ApiOperation("条件分页查询员工")
    public Result<PageResult> queryList(@RequestBody EmployeePageQueryDTO dto){
        PageResult pageResult=employeeService.queryList(dto);
        return Result.success(pageResult);
    }

    @PostMapping("/add")
    @ApiOperation("新增员工")
    public Result add(@RequestBody EmployeeAddDTO dto){
        return employeeService.add(dto);
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation("删除员工")
    public Result delete(@PathVariable String id){
        return employeeService.deleteById(id);
    }

    @PutMapping("/update")
    @ApiOperation("修改员工信息")
    public Result update(@RequestBody Employee employee){
        return employeeService.updateEmp(employee);
    }

    @PutMapping("/resetPassword")
    @ApiOperation("重置员工密码")
    public Result resetPassword(@RequestBody ResetPasswordDTO dto){
        log.info("重置员工密码：{}", dto);
        return employeeService.resetPassword(dto);
    }

}
