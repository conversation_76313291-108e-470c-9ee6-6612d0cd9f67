package com.gymguy.employee.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gymguy.constant.EmployeeStatusConstant;
import com.gymguy.constant.JwtClaimsConstant;
import com.gymguy.dto.EmployeeAddDTO;
import com.gymguy.dto.EmployeeLoginDTO;
import com.gymguy.dto.EmployeePageQueryDTO;
import com.gymguy.dto.ResetPasswordDTO;
import com.gymguy.employee.mapper.EmployeeMapper;
import com.gymguy.employee.service.EmployeeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.entity.Employee;
import com.gymguy.exception.AddException;
import com.gymguy.exception.LoginFailedException;
import com.gymguy.exception.PasswordErrorException;
import com.gymguy.properties.JwtProperties;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.utils.JwtUtil;
import com.gymguy.vo.EmployeeLoginVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 员工信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements EmployeeService {
    @Autowired
    private JwtProperties jwtProperties;

    @Override
    public Employee findById(String id) {
        Employee employee = this.getById(id);
        return employee;
    }

    @Override
    public EmployeeLoginVO login(EmployeeLoginDTO employeeLoginDTO) {
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Employee::getUsername, employeeLoginDTO.getUsername());
        Employee employee = this.getOne(wrapper);
        if(employee == null){
            throw new LoginFailedException("用户不存在");
        }
        String password = DigestUtils.md5DigestAsHex(employeeLoginDTO.getPassword().getBytes());
        if(!password.equals(employee.getPassword())){
            throw new PasswordErrorException("密码错误");
        }
        if(employee.getStatus() != EmployeeStatusConstant.ENABLE){
            throw new LoginFailedException("状态异常");
        }
        //生成jwtToken
        Map<String, Object> map = new HashMap<>();
        map.put(JwtClaimsConstant.STAFF_ID,  employee.getStaffId());
        String token = JwtUtil.createJWT(jwtProperties.getEmployeeSecretKey(),
                jwtProperties.getEmployeeTtl(),
                map);
        EmployeeLoginVO employeeLoginVO = EmployeeLoginVO.builder()
                .employeeToken(token)
                .name(employee.getName())
                .role(employee.getRole())
                .avatar(employee.getAvatar())
                .build();
        return employeeLoginVO;
    }

    /**
     * 条件分页查询
     * @param dto
     * @return
     */
    @Override
    public PageResult queryList(EmployeePageQueryDTO dto) {
        // 查询条件
        LambdaQueryWrapper<Employee> wrapper = new LambdaQueryWrapper<>();
        if(dto.getName() != null && dto.getName()!=""){
            wrapper.like(Employee::getName,dto.getName());
        }
        if(dto.getPhone() != null && dto.getPhone()!=""){
            wrapper.like(Employee::getPhone,dto.getPhone());
        }
        wrapper.orderByDesc(Employee::getHireDate);
        // 分页查询
        Page<Employee> page = new Page<>(dto.getPage(),dto.getPageSize());
        page=page(page,wrapper);
        // 封装结果
        PageResult pageResult = new PageResult(page.getTotal(),page.getRecords());
        return pageResult;
    }

    /**
     * 新增员工
     * @param dto
     */
    @Override
    public Result add(EmployeeAddDTO dto) {
        String phone = dto.getPhone();
        if(getOne(new LambdaQueryWrapper<Employee>().eq(Employee::getPhone,phone))!=null){
            throw new AddException("手机号已存在");
        }
        Employee employee = new Employee();
        BeanUtils.copyProperties(dto,employee);
        // 设置状态
        employee.setStatus(EmployeeStatusConstant.ENABLE);
        // 设置默认密码
        employee.setPassword(DigestUtils.md5DigestAsHex("123456".getBytes()));
        employee.setCreatedTime(LocalDateTime.now());
        boolean save = save(employee);
        if(!save){
            throw new AddException("新增员工失败");
        }
        return Result.success();
    }

    @Override
    public Result deleteById(String id) {
        boolean removeById = removeById(id);
        if(!removeById){
            return Result.error("删除员工失败");
        }
        return Result.success();
    }

    @Override
    public Result updateEmp(Employee employee) {
        String password = employee.getPassword();
        if(password!=null && !"".equals(password)){
            employee.setPassword(DigestUtils.md5DigestAsHex(password.getBytes()));
        }
        boolean res = updateById(employee);
        if(!res){
            return Result.error("修改员工信息失败");
        }
        return Result.success();
    }

    @Override
    public Result resetPassword(ResetPasswordDTO dto) {
        Employee employee = getById(dto.getId());
        if (employee == null) {
            return Result.error("员工不存在");
        }

        // 加密新密码
        String encryptedPassword = DigestUtils.md5DigestAsHex(dto.getNewPassword().getBytes());
        employee.setPassword(encryptedPassword);

        boolean success = updateById(employee);
        if (success) {
            return Result.success("密码重置成功");
        } else {
            return Result.error("密码重置失败");
        }
    }
}
