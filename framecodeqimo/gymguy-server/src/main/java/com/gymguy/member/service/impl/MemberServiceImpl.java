package com.gymguy.member.service.impl;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gymguy.card.service.MemberCardService;
import com.gymguy.dto.AddMemberDTO;
import com.gymguy.dto.MemberPageQueryDTO;
import com.gymguy.dto.UpdateMemberDTO;
import com.gymguy.entity.Member;
import com.gymguy.entity.MemberCard;
import com.gymguy.exception.MemberAddException;
import com.gymguy.exception.MemberUpdateException;
import com.gymguy.member.mapper.MemberMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.member.service.MemberService;
import com.gymguy.result.PageResult;
import com.gymguy.vo.MemberDetailVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 会员基础信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class MemberServiceImpl extends ServiceImpl<MemberMapper, Member> implements MemberService {
    @Autowired
    private MemberCardService memberCardService;
    /**
     * 员工分页查询
     * @param dto
     * @return
     */
    @Override
    public PageResult queryList(MemberPageQueryDTO dto) {
        LambdaQueryWrapper<Member> wrapper = new LambdaQueryWrapper<>();
        if(dto.getName() != null && dto.getName()!=""){
            wrapper.like(Member::getName,dto.getName());
        }
        if(dto.getPhone() != null && dto.getPhone()!=""){
            wrapper.like(Member::getPhone,dto.getPhone());
        }
        Page<Member> page = new Page<>(dto.getPage(),dto.getPageSize());
        page=page(page,wrapper);
        PageResult pageResult = new PageResult(page.getTotal(),page.getRecords());
        return pageResult;
    }

    /**
     * 会员信息修改
     * @param updateMemberDTO
     */
    @Override
    public void updateMember(UpdateMemberDTO updateMemberDTO) {
        Member member = new Member();
        if(updateMemberDTO.getMemberId() == null || updateMemberDTO.getMemberId()==""){
            throw new MemberUpdateException("会员ID不能为空");
        }
        BeanUtils.copyProperties(updateMemberDTO,member);
        member.setUpdatedTime(LocalDateTime.now());
        try {
            updateById(member);
        }catch (Exception e){
            throw new MemberUpdateException("会员信息修改失败");
        }
    }

    /**
     * 添加会员
     * @param addMemberDTO
     */
    @Override
    public void addMember(AddMemberDTO addMemberDTO) {
        Member member = new Member();
        BeanUtils.copyProperties(addMemberDTO,member);
        member.setMemberId(UUID.randomUUID().toString());
        member.setCreatedTime(LocalDateTime.now());
        member.setUpdatedTime(LocalDateTime.now());
        String md5Password = DigestUtils.md5DigestAsHex(addMemberDTO.getPassword().getBytes());
        member.setPassword(md5Password);
        try {
            save(member);
        }catch (Exception e){
            throw new MemberAddException("会员添加失败");
        }
    }

    /**
     * 根据会员id查询会员详细信息
     * @param id
     * @return
     */
    @Override
    public MemberDetailVO getMemberDetailById(String id) {
        Member member = getById(id);
        if(member == null){
            throw new RuntimeException("会员不存在");
        }
        MemberDetailVO memberDetailVO = new MemberDetailVO();
        BeanUtils.copyProperties(member,memberDetailVO);
        List<MemberCard> memberCards =memberCardService.getByMemberId(id);
        memberDetailVO.setMemberCards(memberCards);
        return memberDetailVO;
    }
}
