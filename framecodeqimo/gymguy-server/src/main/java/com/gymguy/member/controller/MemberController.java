package com.gymguy.member.controller;


import com.gymguy.dto.AddMemberDTO;
import com.gymguy.dto.MemberPageQueryDTO;
import com.gymguy.dto.UpdateMemberDTO;
import com.gymguy.member.service.MemberService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.MemberDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 会员基础信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@RestController
@RequestMapping("/admin/member")
@Slf4j
@Api(tags = "会员相关接口")
public class MemberController {
    @Autowired
    private MemberService memberService;
    @ApiOperation("会员分页查询")
    @PostMapping("/list")
    public Result<PageResult> queryList(MemberPageQueryDTO dto){
        log.info("员工分页查询,{}",dto);
        PageResult pageResult=memberService.queryList(dto);
        return Result.success(pageResult);
    }

    @ApiOperation("会员信息修改")
    @PutMapping("/update")
    public Result updateMember(@RequestBody UpdateMemberDTO updateMemberDTO){
        log.info("会员信息修改"+updateMemberDTO);
        memberService.updateMember(updateMemberDTO);
        return Result.success();
    }

    @ApiOperation("添加会员")
    @PostMapping("/add")
    public Result addMember(@RequestBody AddMemberDTO addMemberDTO){
        log.info("添加会员"+addMemberDTO);
        memberService.addMember(addMemberDTO);
        return Result.success();
    }

    @ApiOperation("删除会员")
    @DeleteMapping("/delete/{id}")
    public Result deleteMember(@PathVariable String id){
        log.info("删除会员"+id);
        memberService.removeById(id);
        return Result.success();
    }

    @ApiOperation("根据会员id查询会员详细信息")
    @GetMapping("/memberDetail/{id}")
    public Result<MemberDetailVO> memberDetail(@PathVariable String id){
        log.info("根据会员id查询会员详细信息"+id);
        return Result.success(memberService.getMemberDetailById(id));
    }
}
