package com.gymguy.member.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.AddMemberDTO;
import com.gymguy.dto.MemberPageQueryDTO;
import com.gymguy.dto.UpdateMemberDTO;
import com.gymguy.entity.Member;
import com.gymguy.result.PageResult;
import com.gymguy.vo.MemberDetailVO;

/**
 * <p>
 * 会员基础信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface MemberService extends IService<Member> {
    /**
     * 员工分页查询
     * @param dto
     * @return
     */
    PageResult queryList(MemberPageQueryDTO dto);

    /**
     * 会员信息修改
     * @param updateMemberDTO
     */
    void updateMember(UpdateMemberDTO updateMemberDTO);

    /**
     * 添加会员
     * @param addMemberDTO
     */
    void addMember(AddMemberDTO addMemberDTO);

    /**
     * 根据会员id查询会员详细信息
     * @param id
     * @return
     */
    MemberDetailVO getMemberDetailById(String id);
}
