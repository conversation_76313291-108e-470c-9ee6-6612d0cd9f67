package com.gymguy.equipment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.EquipmentAddDTO;
import com.gymguy.dto.EquipmentPageQueryDTO;
import com.gymguy.entity.Equipment;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;

/**
 * <p>
 * 器材信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface EquipmentService extends IService<Equipment> {

    /**
     * 分页查询器材
     * @param dto
     * @return
     */
    PageResult queryList(EquipmentPageQueryDTO dto);

    /**
     * 添加器材
     * @param dto
     * @return
     */
    Result<String> add(EquipmentAddDTO dto);

    /**
     * 更新器材状态
     * @param id
     * @param status
     * @return
     */
    Result<String> updateStatus(Integer id, Integer status);

    /**
     * 删除器材
     * @param id
     * @return
     */
    Result<String> deleteById(Integer id);
}
