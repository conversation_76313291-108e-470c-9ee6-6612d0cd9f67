package com.gymguy.equipment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.dto.EquipmentAddDTO;
import com.gymguy.dto.EquipmentPageQueryDTO;
import com.gymguy.entity.Equipment;
import com.gymguy.equipment.mapper.EquipmentMapper;
import com.gymguy.equipment.service.EquipmentService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 器材信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@Service
public class EquipmentServiceImpl extends ServiceImpl<EquipmentMapper, Equipment> implements EquipmentService {

    @Override
    public PageResult queryList(EquipmentPageQueryDTO dto) {
        log.info("分页查询器材信息：{}", dto);
        
        Page<Equipment> page = new Page<>(dto.getPage(), dto.getPageSize());
        LambdaQueryWrapper<Equipment> wrapper = new LambdaQueryWrapper<>();
        
        // 条件查询
        wrapper.like(dto.getName() != null && !dto.getName().isEmpty(), Equipment::getName, dto.getName())
               .eq(dto.getType() != null && !dto.getType().isEmpty(), Equipment::getType, dto.getType())
               .eq(dto.getStatus() != null, Equipment::getStatus, dto.getStatus())
               .orderByDesc(Equipment::getCreatedTime);
        
        Page<Equipment> pageResult = page(page, wrapper);
        
        return new PageResult(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public Result<String> add(EquipmentAddDTO dto) {
        log.info("添加器材：{}", dto);
        
        Equipment equipment = new Equipment();
        BeanUtils.copyProperties(dto, equipment);
        equipment.setStatus(1); // 默认状态为正常
        equipment.setCreatedTime(LocalDateTime.now());
        
        boolean success = save(equipment);
        if (success) {
            return Result.success("添加器材成功");
        } else {
            return Result.error("添加器材失败");
        }
    }

    @Override
    public Result<String> updateStatus(Integer id, Integer status) {
        log.info("更新器材状态：id={}, status={}", id, status);
        
        Equipment equipment = getById(id);
        if (equipment == null) {
            return Result.error("器材不存在");
        }
        
        equipment.setStatus(status);
        boolean success = updateById(equipment);
        
        if (success) {
            return Result.success("更新状态成功");
        } else {
            return Result.error("更新状态失败");
        }
    }

    @Override
    public Result<String> deleteById(Integer id) {
        log.info("删除器材：id={}", id);
        
        boolean success = removeById(id);
        if (success) {
            return Result.success("删除器材成功");
        } else {
            return Result.error("删除器材失败");
        }
    }
}
