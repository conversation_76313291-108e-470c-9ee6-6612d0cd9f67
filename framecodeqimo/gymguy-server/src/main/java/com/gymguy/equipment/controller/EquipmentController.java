package com.gymguy.equipment.controller;

import com.gymguy.dto.EquipmentAddDTO;
import com.gymguy.dto.EquipmentPageQueryDTO;
import com.gymguy.equipment.service.EquipmentService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 器材信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/admin/equipment")
@Api(tags = "器材管理接口")
public class EquipmentController {

    @Autowired
    private EquipmentService equipmentService;

    /**
     * 分页查询器材
     * @param dto
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("分页查询器材")
    public Result<PageResult> page(EquipmentPageQueryDTO dto) {
        log.info("分页查询器材：{}", dto);
        PageResult pageResult = equipmentService.queryList(dto);
        return Result.success(pageResult);
    }

    /**
     * 添加器材
     * @param dto
     * @return
     */
    @PostMapping
    @ApiOperation("添加器材")
    public Result<String> add(@RequestBody EquipmentAddDTO dto) {
        log.info("添加器材：{}", dto);
        return equipmentService.add(dto);
    }

    /**
     * 更新器材状态
     * @param id
     * @param status
     * @return
     */
    @PutMapping("/{id}/status/{status}")
    @ApiOperation("更新器材状态")
    public Result<String> updateStatus(@PathVariable Integer id, @PathVariable Integer status) {
        log.info("更新器材状态：id={}, status={}", id, status);
        return equipmentService.updateStatus(id, status);
    }

    /**
     * 删除器材
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除器材")
    public Result<String> delete(@PathVariable Integer id) {
        log.info("删除器材：id={}", id);
        return equipmentService.deleteById(id);
    }
}
