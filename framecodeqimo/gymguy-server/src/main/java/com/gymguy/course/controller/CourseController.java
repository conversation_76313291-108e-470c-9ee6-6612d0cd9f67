package com.gymguy.course.controller;

import com.gymguy.course.service.CourseService;
import com.gymguy.dto.CourseAddDTO;
import com.gymguy.dto.CoursePageQueryDTO;
import com.gymguy.dto.CourseUpdateDTO;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CourseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    @PostMapping("/add")
    public Result addCourse(@RequestBody CourseAddDTO courseAddDTO) {
        return courseService.addCourse(courseAddDTO);
    }

    @GetMapping("/{id}")
    public Result<CourseVO> getCourseById(@PathVariable Integer id) {
        return courseService.getCourseById(id);
    }

    @GetMapping("/list")
    public Result<PageResult> getCourseList(@RequestBody CoursePageQueryDTO coursePageQueryDTO) {
        log.info("分页查询课程信息:{}", coursePageQueryDTO);
        PageResult courseList = courseService.getCourseList(coursePageQueryDTO);
        return Result.success(courseList);
    }

    @PutMapping("/update")
    public Result updateCourse(@RequestBody CourseUpdateDTO courseUpdateDTO) {
        return courseService.updateCourse(courseUpdateDTO);
    }

    @Scheduled(cron = "0/10 * * * * ? ")
    public void autoUpdateCourseStatus(){
        courseService.autoUpdateCourseStatus();
    }
}
