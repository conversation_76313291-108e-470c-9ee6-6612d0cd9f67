package com.gymguy.course.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.CourseAddDTO;
import com.gymguy.dto.CoursePageQueryDTO;
import com.gymguy.dto.CourseUpdateDTO;
import com.gymguy.entity.Course;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CourseVO;

public interface CourseService extends IService<Course> {
    Result addCourse(CourseAddDTO courseAddDTO);

    Result<CourseVO> getCourseById(Integer id);

    PageResult getCourseList(CoursePageQueryDTO coursePageQueryDTO);

    Result updateCourse(CourseUpdateDTO courseUpdateDTO);

    void autoUpdateCourseStatus();
}
