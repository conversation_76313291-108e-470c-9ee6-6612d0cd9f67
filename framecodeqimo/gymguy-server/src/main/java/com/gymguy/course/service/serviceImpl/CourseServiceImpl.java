package com.gymguy.course.service.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.coach.mapper.CoachMapper;
import com.gymguy.constant.CourseStatusConstant;
import com.gymguy.dto.CourseAddDTO;
import com.gymguy.dto.CoursePageQueryDTO;
import com.gymguy.dto.CourseUpdateDTO;
import com.gymguy.entity.Course;
import com.gymguy.course.mapper.CourseMapper;
import com.gymguy.course.service.CourseService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CourseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;


import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CourseServiceImpl extends ServiceImpl<CourseMapper, Course> implements CourseService{

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private CoachMapper coachMapper;

    /**
     * 添加课程
     * @param courseAddDTO
     * @return
     */
    @Override
    public Result addCourse(CourseAddDTO courseAddDTO) {
        Course course = new Course();
        BeanUtils.copyProperties(courseAddDTO, course);
        course.setCreatedAt(LocalDateTime.now());
        course.setUpdatedAt(LocalDateTime.now());
        log.info("将添加的课程信息:course={}", course);
        int raw = courseMapper.insert(course);
        return raw == 1 ? Result.success("添加课程成功") : Result.error("添加课程失败");
    }

    /**
     * 获取课程信息
     * @param id
     * @return
     */
    @Override
    public Result<CourseVO> getCourseById(Integer id) {
        log.info("根据id获取课程信息:id={}", id);
        Course course = courseMapper.selectById(id);

        CourseVO courseVO = new CourseVO();
        BeanUtils.copyProperties(course, courseVO);

        courseVO.setCoachName(coachMapper.selectById(course.getCoachId()).getName());

        log.info("查询课程信息:course={}", courseVO);
        return Result.success( courseVO);
    }

    @Override
    public PageResult getCourseList(CoursePageQueryDTO coursePageQueryDTO) {
        Page<Course> coursePage = new Page<>(coursePageQueryDTO.getPage(), coursePageQueryDTO.getPageSize());

        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        // 如果提供了教练姓名，需要关联教练表进行模糊查询
        if (StringUtils.hasText(coursePageQueryDTO.getCoachName())) {
            wrapper.inSql(Course::getId, "SELECT id FROM course WHERE coach_id IN (SELECT id FROM coach WHERE name LIKE '%" + coursePageQueryDTO.getCoachName() + "%')");
        }
        wrapper.like(StringUtils.hasText(coursePageQueryDTO.getName()), Course::getName, coursePageQueryDTO.getName())
                .eq(StringUtils.hasText(coursePageQueryDTO.getLocation()), Course::getLocation, coursePageQueryDTO.getLocation())
                .eq(coursePageQueryDTO.getStatus() != null, Course::getStatus, coursePageQueryDTO.getStatus())
                .ge(coursePageQueryDTO.getStartTime() != null, Course::getSchedule, coursePageQueryDTO.getStartTime())
                .le(coursePageQueryDTO.getEndTime() != null, Course::getSchedule, coursePageQueryDTO.getEndTime())
                .eq(coursePageQueryDTO.getMaxCapacity() != null, Course::getMaxCapacity, coursePageQueryDTO.getMaxCapacity())
                .eq(coursePageQueryDTO.getDuration() != null, Course::getDuration, coursePageQueryDTO.getDuration());

        //执行分页查询
        Page<Course> selectPage = courseMapper.selectPage(coursePage, wrapper);

        //封装查询到的课程信息到CourseVO类型的List中
        List<CourseVO> courseVOList = selectPage.getRecords().stream().map(course -> {
            CourseVO courseVO = new CourseVO();
            BeanUtils.copyProperties(course, courseVO);
            courseVO.setCoachName(coachMapper.selectById(course.getCoachId()).getName());
            return courseVO;
        }).collect(Collectors.toList());
        return new PageResult(selectPage.getTotal(), courseVOList);
    }

    /**
     * 修改课程信息
     * @param courseUpdateDTO
     * @return
     */
    @Override
    public Result updateCourse(CourseUpdateDTO courseUpdateDTO) {
        LambdaUpdateWrapper<Course> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Course::getId, courseUpdateDTO.getId())
                .set(StringUtils.hasText(courseUpdateDTO.getName()),Course::getName, courseUpdateDTO.getName())
                .set(courseUpdateDTO.getCoachId() != null,Course::getCoachId, courseUpdateDTO.getCoachId())
                .set(StringUtils.hasText(courseUpdateDTO.getSchedule()),Course::getSchedule, courseUpdateDTO.getSchedule())
                .set(courseUpdateDTO.getDuration() != null,Course::getDuration, courseUpdateDTO.getDuration())
                .set(courseUpdateDTO.getMaxCapacity() != null,Course::getMaxCapacity, courseUpdateDTO.getMaxCapacity())
                .set(courseUpdateDTO.getBookedCount() != null,Course::getBookedCount, courseUpdateDTO.getBookedCount())
                .set(StringUtils.hasText(courseUpdateDTO.getLocation()),Course::getLocation, courseUpdateDTO.getLocation())
                .set(courseUpdateDTO.getStatus() != null,Course::getStatus, courseUpdateDTO.getStatus())
                .set(Course::getUpdatedAt, LocalDateTime.now());

        int raw = courseMapper.update(wrapper);

        return raw == 1 ? Result.success("更新课程信息成功") : Result.error("更新课程信息失败");
    }

    /**
     * 定时任务，自动更新课程状态
     */
    @Override
    @Transactional
    public void autoUpdateCourseStatus() {
        //1.查询数据库中状态为未开始以及开始时间小于等于当前时间的课程
        LambdaQueryWrapper<Course> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Course::getStatus, CourseStatusConstant.UNSTARTED)
                .le(Course::getSchedule, LocalDateTime.now());
        List<Course> courseList = courseMapper.selectList(wrapper);
        log.info("查询到未开始课程:{}", courseList);

        //2.抽取出未开始课程的id
        List<Integer> courseIdList = courseList.stream().map(Course::getId).collect(Collectors.toList());
        log.info("未开始课程的id:{}", courseIdList);

        if(!courseIdList.isEmpty()){
            //3.修改课程状态为进行中
            LambdaUpdateWrapper<Course> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(Course::getId, courseIdList)
                    .set(Course::getStatus, CourseStatusConstant.IN_PROGRESS);
            courseMapper.update(updateWrapper);
        }



        //1.查询数据库中状态为进行中以及结束时间小于等于当前时间的课程
        LambdaQueryWrapper<Course> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(Course::getStatus, CourseStatusConstant.IN_PROGRESS)
                .apply("schedule + INTERVAL duration MINUTE <= {0}", LocalDateTime.now());
        List<Course> courseList1 = courseMapper.selectList(wrapper1);
        log.info("查询到进行中课程:{}", courseList1);

        //2.抽取出进行中课程的id
        List<Integer> courseIdList1 = courseList1.stream().map(Course::getId).collect(Collectors.toList());
        log.info("进行中课程的id:{}", courseIdList1);

        if(!courseIdList1.isEmpty()){
            //3.修改课程状态为已完成
            LambdaUpdateWrapper<Course> updateWrapper1 = new LambdaUpdateWrapper<>();
            updateWrapper1.in(Course::getId, courseIdList1)
                    .set(Course::getStatus, CourseStatusConstant.FINISHED);
            courseMapper.update(updateWrapper1);
        }
    }
}
