package com.gymguy.card.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.AddMemberCardDTO;
import com.gymguy.dto.MemberCardPageQueryDTO;
import com.gymguy.entity.MemberCard;
import com.gymguy.result.PageResult;

import java.util.List;

/**
 * <p>
 * 会员卡信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface MemberCardService extends IService<MemberCard> {
    /**
     * 会员卡分页查询
     * @param dto
     * @return
     */
    PageResult queryList(MemberCardPageQueryDTO dto);

    /**
     * 使用会员卡
     * @param id
     */
    void useMemberCared(String id);

    /**
     * 冻结会员卡
     * @param id
     */
    void freezeMemberCared(String id);

    /**
     * 解冻会员卡
     * @param id
     */
    void unfreezeMemberCared(String id);

    /**
     * 办理会员卡
     * @param addMemberCardDTO
     */
    void addMemberCard(AddMemberCardDTO addMemberCardDTO);

    /**
     * 根据会员id查询会员卡信息
     * @param memberId
     * @return
     */
    List<MemberCard> getByMemberId(String memberId);
}
