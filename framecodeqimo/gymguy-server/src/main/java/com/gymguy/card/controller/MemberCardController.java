package com.gymguy.card.controller;


import com.gymguy.card.service.MemberCardService;
import com.gymguy.dto.AddMemberCardDTO;
import com.gymguy.dto.MemberCardPageQueryDTO;
import com.gymguy.entity.MemberCard;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 会员卡信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@RestController
@RequestMapping("/admin/member-card")
public class MemberCardController {
    @Autowired
    private MemberCardService memberCardService;

    @ApiOperation("会员卡分页查询")
    @PostMapping("/list")
    public Result<PageResult> queryList(MemberCardPageQueryDTO dto){
        PageResult pageResult=memberCardService.queryList(dto);
        return Result.success(pageResult);
    }

    @ApiOperation("使用会员卡")
    @PutMapping("/use")
    public Result useMemberCared(String id){
        memberCardService.useMemberCared(id);
        return Result.success();
    }

    @ApiOperation("冻结会员卡")
    @PutMapping("/freeze")
    public Result freezeMemberCared(String id){
        memberCardService.freezeMemberCared(id);
        return Result.success();
    }

    @ApiOperation("解冻会员卡")
    @PutMapping("/unfreeze")
    public Result unfreezeMemberCared(String id){
        memberCardService.unfreezeMemberCared(id);
        return Result.success();
    }

    @ApiOperation("办理会员卡")
    @PostMapping("/add")
    public Result addMemberCard(@RequestBody AddMemberCardDTO addMemberCardDTO){
        memberCardService.addMemberCard(addMemberCardDTO);
        return Result.success();
    }

}
