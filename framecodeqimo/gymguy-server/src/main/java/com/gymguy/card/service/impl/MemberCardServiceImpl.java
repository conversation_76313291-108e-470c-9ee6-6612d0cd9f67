package com.gymguy.card.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gymguy.card.mapper.MemberCardMapper;
import com.gymguy.card.service.MemberCardService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.constant.MemberCardFreezeException;
import com.gymguy.constant.MemberCardStatusConstant;
import com.gymguy.constant.MemberCardTypeConstant;
import com.gymguy.dto.AddMemberCardDTO;
import com.gymguy.dto.MemberCardPageQueryDTO;
import com.gymguy.entity.Member;
import com.gymguy.entity.MemberCard;
import com.gymguy.exception.AddMemberException;
import com.gymguy.exception.MemberCardUseException;
import com.gymguy.member.service.MemberService;
import com.gymguy.result.PageResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 会员卡信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class MemberCardServiceImpl extends ServiceImpl<MemberCardMapper, MemberCard> implements MemberCardService {
    @Autowired
    private MemberService memberService;
    @Autowired
    private MemberCardMapper memberCardMapper;
    /**
     * 会员卡分页查询
     * @param dto
     * @return
     */
    @Override
    public PageResult queryList(MemberCardPageQueryDTO dto) {
        LambdaQueryWrapper<MemberCard> wrapper = new LambdaQueryWrapper<>();
        //筛选当前会员Id的会员卡
        if(dto.getMemberId() != null && dto.getMemberId()!=""){
            wrapper.eq(MemberCard::getMemberId,dto.getMemberId());
        }
        //筛选当前会员卡类型
        if (dto.getCardType() != null){
            wrapper.eq(MemberCard::getCardType,dto.getCardType());
        }
        //筛选会员卡的状态
        if (dto.getStatus() != null){
            wrapper.eq(MemberCard::getStatus,dto.getStatus());
        }
        Page<MemberCard> page = new Page<>(dto.getPage(),dto.getPageSize());
        page=page(page,wrapper);
        PageResult pageResult = new PageResult(page.getTotal(),page.getRecords());
        return pageResult;
    }

    /**
     * 使用健身卡
     * @param id
     */
    @Override
    public void useMemberCared(String id) {
        MemberCard memberCard = getById(id);
        if (memberCard == null) {
            throw new MemberCardUseException("会员卡不存在");
        }
        if (memberCard.getStatus() != MemberCardStatusConstant.EFFECTIVE) {
            throw new MemberCardUseException("会员卡状态异常");
        }
        LocalDate now = LocalDate.now();
        if (now.isAfter(memberCard.getEndTime())) {
            memberCard.setStatus(MemberCardStatusConstant.EXPIRED);
            updateById(memberCard);
            throw new MemberCardUseException("会员卡已过期");
        }
        if (memberCard.getCardType() == MemberCardTypeConstant.USAGE_CARD) {
            if (memberCard.getRemainingUsage() <= 0) {
                throw new MemberCardUseException("会员卡次卡次数不足");
            }
            memberCard.setRemainingUsage(memberCard.getRemainingUsage() - 1);
            updateById(memberCard);
        }
    }

    /**
     * 冻结会员卡
     * @param id
     */
    @Override
    public void freezeMemberCared(String id) {
        MemberCard memberCard = getById(id);
        if (memberCard == null) {
            throw new MemberCardFreezeException("会员卡不存在");
        }
        if(memberCard.getStatus() == MemberCardStatusConstant.CANCELED){
            throw new MemberCardFreezeException("会员卡已注销");
        }
        memberCard.setStatus(MemberCardStatusConstant.FREEZED);
        updateById(memberCard);
    }

    /**
     * 解冻会员卡
     * @param id
     */
    @Override
    public void unfreezeMemberCared(String id) {
        MemberCard memberCard = getById(id);
        if(memberCard == null){
            throw new MemberCardFreezeException("会员卡不存在");
        }
        if(memberCard.getStatus() != MemberCardStatusConstant.FREEZED){
            throw new MemberCardFreezeException("会员卡状态异常");
        }
        memberCard.setStatus(MemberCardStatusConstant.EFFECTIVE);
        LocalDate now = LocalDate.now();
        if (now.isAfter(memberCard.getEndTime())) {
            memberCard.setStatus(MemberCardStatusConstant.EXPIRED);
        }
        updateById(memberCard);
    }

    /**
     * 办理会员卡
     * @param addMemberCardDTO
     */
    @Override
    public void addMemberCard(AddMemberCardDTO addMemberCardDTO) {
        Member member = memberService.getById(addMemberCardDTO.getMemberId());
        if(member == null){
            throw new AddMemberException("会员不存在");
        }
        MemberCard memberCard = new MemberCard();
        BeanUtils.copyProperties(addMemberCardDTO,memberCard);
        if(addMemberCardDTO.getCardType() == MemberCardTypeConstant.USAGE_CARD){
            //如果是次卡，剩余次数等于总次数
            memberCard.setRemainingUsage(addMemberCardDTO.getTotalUsage());
            //次卡默认使用时间为一年
            memberCard.setEndTime(memberCard.getStartTime().plusYears(1));
        }else if(addMemberCardDTO.getCardType() == MemberCardTypeConstant.MONTH_CARD){
            //如果是月卡，结束时间是当前时间加一个月
            memberCard.setEndTime(memberCard.getStartTime().plusMonths(1));
        }else if(addMemberCardDTO.getCardType() == MemberCardTypeConstant.SEASON_CARD){
            //如果是季卡，结束时间是当前时间加三个月
            memberCard.setEndTime(memberCard.getStartTime().plusMonths(3));
        }else{
            //如果是年卡，结束时间是当前时间加一年
            memberCard.setEndTime(memberCard.getStartTime().plusYears(1));
        }
        memberCard.setStatus(MemberCardStatusConstant.EFFECTIVE);
        save(memberCard);
    }

    /**
     * 根据会员id查询会员卡信息
     * @param memberId
     * @return
     */
    @Override
    public List<MemberCard> getByMemberId(String memberId) {
        LambdaQueryWrapper<MemberCard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberCard::getMemberId,memberId);
        List<MemberCard> memberCardList = memberCardMapper.selectList(wrapper);
        return memberCardList;
    }
}
