package com.gymguy.notice.controller;

import com.gymguy.dto.NoticeAddDTO;
import com.gymguy.dto.NoticePageQueryDTO;
import com.gymguy.notice.service.NoticeService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 公告信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@RestController
@RequestMapping("/admin/notice")
@Api(tags = "公告管理接口")
public class NoticeController {

    @Autowired
    private NoticeService noticeService;

    /**
     * 分页查询公告
     * @param dto
     * @return
     */
    @GetMapping("/page")
    @ApiOperation("分页查询公告")
    public Result<PageResult> page(NoticePageQueryDTO dto) {
        log.info("分页查询公告：{}", dto);
        PageResult pageResult = noticeService.queryList(dto);
        return Result.success(pageResult);
    }

    /**
     * 添加公告
     * @param dto
     * @return
     */
    @PostMapping
    @ApiOperation("添加公告")
    public Result<String> add(@RequestBody NoticeAddDTO dto) {
        log.info("添加公告：{}", dto);
        return noticeService.add(dto);
    }

    /**
     * 更新公告
     * @param dto
     * @return
     */
    @PutMapping
    @ApiOperation("更新公告")
    public Result<String> update(@RequestBody NoticeAddDTO dto) {
        log.info("更新公告：{}", dto);
        return noticeService.update(dto);
    }

    /**
     * 更新公告状态
     * @param id
     * @param status
     * @return
     */
    @PutMapping("/{id}/status/{status}")
    @ApiOperation("更新公告状态")
    public Result<String> updateStatus(@PathVariable Integer id, @PathVariable Integer status) {
        log.info("更新公告状态：id={}, status={}", id, status);
        return noticeService.updateStatus(id, status);
    }

    /**
     * 置顶/取消置顶公告
     * @param id
     * @param isTop
     * @return
     */
    @PutMapping("/{id}/top/{isTop}")
    @ApiOperation("置顶/取消置顶公告")
    public Result<String> updateTop(@PathVariable Integer id, @PathVariable Integer isTop) {
        log.info("更新公告置顶状态：id={}, isTop={}", id, isTop);
        return noticeService.updateTop(id, isTop);
    }

    /**
     * 删除公告
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @ApiOperation("删除公告")
    public Result<String> delete(@PathVariable Integer id) {
        log.info("删除公告：id={}", id);
        return noticeService.deleteById(id);
    }


}
