package com.gymguy.notice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.NoticeAddDTO;
import com.gymguy.dto.NoticePageQueryDTO;
import com.gymguy.entity.Notice;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;

/**
 * <p>
 * 公告信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
public interface NoticeService extends IService<Notice> {

    /**
     * 分页查询公告
     * @param dto
     * @return
     */
    PageResult queryList(NoticePageQueryDTO dto);

    /**
     * 添加公告
     * @param dto
     * @return
     */
    Result<String> add(NoticeAddDTO dto);

    /**
     * 更新公告
     * @param dto
     * @return
     */
    Result<String> update(NoticeAddDTO dto);

    /**
     * 更新公告状态
     * @param id
     * @param status
     * @return
     */
    Result<String> updateStatus(Integer id, Integer status);

    /**
     * 删除公告
     * @param id
     * @return
     */
    Result<String> deleteById(Integer id);

    /**
     * 置顶/取消置顶公告
     * @param id
     * @param isTop
     * @return
     */
    Result<String> updateTop(Integer id, Integer isTop);
}
