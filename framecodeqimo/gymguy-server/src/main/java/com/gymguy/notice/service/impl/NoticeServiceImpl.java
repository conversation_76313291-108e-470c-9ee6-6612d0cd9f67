package com.gymguy.notice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.dto.NoticeAddDTO;
import com.gymguy.dto.NoticePageQueryDTO;
import com.gymguy.entity.Notice;
import com.gymguy.notice.mapper.NoticeMapper;
import com.gymguy.notice.service.NoticeService;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 公告信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Slf4j
@Service
public class NoticeServiceImpl extends ServiceImpl<NoticeMapper, Notice> implements NoticeService {

    @Override
    public PageResult queryList(NoticePageQueryDTO dto) {
        log.info("分页查询公告信息：{}", dto);
        
        Page<Notice> page = new Page<>(dto.getPage(), dto.getPageSize());
        LambdaQueryWrapper<Notice> wrapper = new LambdaQueryWrapper<>();
        
        // 条件查询
        wrapper.like(dto.getTitle() != null && !dto.getTitle().isEmpty(), Notice::getTitle, dto.getTitle())
               .eq(dto.getStatus() != null, Notice::getStatus, dto.getStatus())
               .orderByDesc(Notice::getCreatedTime);
        
        Page<Notice> pageResult = page(page, wrapper);
        
        return new PageResult(pageResult.getTotal(), pageResult.getRecords());
    }

    @Override
    public Result<String> add(NoticeAddDTO dto) {
        log.info("添加公告：{}", dto);
        
        Notice notice = new Notice();
        BeanUtils.copyProperties(dto, notice);
        notice.setStatus(1); // 默认状态为发布
        notice.setPublisherId("1"); // 设置发布人ID
        notice.setCreatedTime(LocalDateTime.now());
        notice.setUpdatedTime(LocalDateTime.now());
        
        boolean success = save(notice);
        if (success) {
            return Result.success("添加公告成功");
        } else {
            return Result.error("添加公告失败");
        }
    }

    @Override
    public Result<String> update(NoticeAddDTO dto) {
        log.info("更新公告：{}", dto);

        Notice notice = getById(dto.getNoticeId());
        if (notice == null) {
            return Result.error("公告不存在");
        }

        BeanUtils.copyProperties(dto, notice);
        notice.setUpdatedTime(LocalDateTime.now());

        boolean success = updateById(notice);
        if (success) {
            return Result.success("更新公告成功");
        } else {
            return Result.error("更新公告失败");
        }
    }

    @Override
    public Result<String> updateStatus(Integer id, Integer status) {
        log.info("更新公告状态：id={}, status={}", id, status);
        
        Notice notice = getById(id);
        if (notice == null) {
            return Result.error("公告不存在");
        }
        
        notice.setStatus(status);
        notice.setUpdatedTime(LocalDateTime.now());
        boolean success = updateById(notice);
        
        if (success) {
            return Result.success("更新状态成功");
        } else {
            return Result.error("更新状态失败");
        }
    }

    @Override
    public Result<String> deleteById(Integer id) {
        log.info("删除公告：id={}", id);
        
        boolean success = removeById(id);
        if (success) {
            return Result.success("删除公告成功");
        } else {
            return Result.error("删除公告失败");
        }
    }

    @Override
    public Result<String> updateTop(Integer id, Integer isTop) {
        log.info("更新公告置顶状态：id={}, isTop={}", id, isTop);

        Notice notice = getById(id);
        if (notice == null) {
            return Result.error("公告不存在");
        }

        notice.setIsTop(isTop);
        notice.setUpdatedTime(LocalDateTime.now());
        boolean success = updateById(notice);

        if (success) {
            String action = isTop == 1 ? "置顶" : "取消置顶";
            return Result.success(action + "成功");
        } else {
            return Result.error("置顶状态更新失败");
        }
    }
}
