package com.gymguy.coach.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gymguy.dto.CoachDTO;
import com.gymguy.dto.CoachPageQueryDTO;
import com.gymguy.dto.CoachUpdateDTO;
import com.gymguy.entity.Coach;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CoachVO;
import org.springframework.stereotype.Service;


public interface CoachService extends IService<Coach>{
    PageResult pageQuery(CoachPageQueryDTO coachPageQueryDTO);

    CoachVO getCoachById(Integer id);

    Result addCoach(CoachDTO coachDTO);

    Result updateCoach(CoachUpdateDTO coachUpdateDTO);
}
