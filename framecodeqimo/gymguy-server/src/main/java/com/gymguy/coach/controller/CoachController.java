package com.gymguy.coach.controller;

import com.gymguy.coach.service.CoachService;
import com.gymguy.dto.CoachDTO;
import com.gymguy.dto.CoachPageQueryDTO;
import com.gymguy.dto.CoachUpdateDTO;
import com.gymguy.entity.Coach;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CoachVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Api(tags = "教练相关接口")
@RestController
@RequestMapping("/coach")
public class CoachController {

    @Autowired
    private CoachService coachService;

    @PostMapping("/list")
    @ApiOperation("分页查询教练信息")
    public Result<PageResult> getCoach(@RequestBody CoachPageQueryDTO coachPageQueryDTO){
        log.info("分页查询教练信息");
        PageResult pageResult =coachService.pageQuery(coachPageQueryDTO);
        return Result.success(pageResult);
    }

    @GetMapping("/{id}")
    public Result<CoachVO> getCoachById(@PathVariable Integer id){
        log.info("查询教练信息");
        CoachVO coachVO = coachService.getCoachById(id);
        return Result.success(coachVO);
    }

    @PostMapping("/add")
    public Result addCoach(@RequestBody CoachDTO coachDTO){
        log.info("添加教练信息:{}", coachDTO);
        return coachService.addCoach(coachDTO);
    }


    @PutMapping("/update")
    public Result updateCoach(@RequestBody CoachUpdateDTO coachUpdateDTO){
        log.info("更新教练信息:coachDTO={}", coachUpdateDTO);
        return coachService.updateCoach(coachUpdateDTO);
    }
}
