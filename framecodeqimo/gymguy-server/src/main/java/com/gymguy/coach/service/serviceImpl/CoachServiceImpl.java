package com.gymguy.coach.service.serviceImpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gymguy.coach.mapper.CoachMapper;
import com.gymguy.coach.service.CoachService;
import com.gymguy.constant.CoachStatusConstant;
import com.gymguy.dto.CoachDTO;
import com.gymguy.dto.CoachPageQueryDTO;
import com.gymguy.dto.CoachUpdateDTO;
import com.gymguy.entity.Coach;
import com.gymguy.result.PageResult;
import com.gymguy.result.Result;
import com.gymguy.vo.CoachVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Slf4j
@Service
public class CoachServiceImpl extends ServiceImpl<CoachMapper, Coach> implements CoachService {

    @Autowired
    private CoachMapper coachMapper;

    /**
     * 分页查询教练信息
     * @param coachPageQueryDTO
     * @return
     */
    @Override
    public PageResult pageQuery(CoachPageQueryDTO coachPageQueryDTO) {
        Page<Coach> page = new Page<>(coachPageQueryDTO.getPage(),coachPageQueryDTO.getPageSize());

        //根据姓名模糊查询，根据状态查找在职的教练
        LambdaQueryWrapper<Coach> wrapper = new LambdaQueryWrapper<Coach>()
                .like(StringUtils.isNotBlank(coachPageQueryDTO.getName()), Coach::getName, coachPageQueryDTO.getName())
                .like(StringUtils.isNotBlank(coachPageQueryDTO.getSpecialty()), Coach::getSpecialty, coachPageQueryDTO.getSpecialty())
                .like(StringUtils.isNotBlank(coachPageQueryDTO.getCertification()), Coach::getCertification, coachPageQueryDTO.getCertification())
                .eq(coachPageQueryDTO.getGender() != null, Coach::getGender, coachPageQueryDTO.getGender())
                .eq(Coach::getStatus, coachPageQueryDTO.getStatus());
        Page<Coach> pageResult = coachMapper.selectPage(page, wrapper);

        return new PageResult(pageResult.getTotal(), pageResult.getRecords());
    }

    /**
     * 根据ID查询教练信息
     * @param id
     * @return
     */
    @Override
    public CoachVO getCoachById(Integer id) {
        Coach coach = coachMapper.selectById(id);

        CoachVO coachVO = new CoachVO();
        BeanUtils.copyProperties(coach, coachVO);
        log.info("查询教练信息：{}", coachVO);
        return coachVO;
    }

    /**
     * 新增教练信息
     * @param coachDTO
     * @return
     */
    @Override
    public Result addCoach(CoachDTO coachDTO) {
        Coach coach = new Coach();
        BeanUtils.copyProperties(coachDTO, coach);
        coach.setHireDate(LocalDate.now());
        coach.setStatus(CoachStatusConstant.IN_SERVICE);
        coach.setCreatedAt(LocalDateTime.now());
        coach.setUpdatedAt(LocalDateTime.now());
        log.info("新增教练信息：{}", coach);

        int raw = coachMapper.insert(coach);
        return raw == 1 ? Result.success("添加教练成功") : Result.error("添加教练失败");
    }


    /**
     * 修改教练信息
     * @param coachUpdateDTO
     * @return
     */
    @Override
    public Result updateCoach(CoachUpdateDTO coachUpdateDTO) {
        log.info("将修改的教练信息:coachUpdateDTO={}", coachUpdateDTO);
        //封装修改的参数
        LambdaUpdateWrapper<Coach> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Coach::getId, coachUpdateDTO.getId())
                .set(coachUpdateDTO.getName() != null,Coach::getName, coachUpdateDTO.getName())
                .set(coachUpdateDTO.getPhone() != null,Coach::getPhone, coachUpdateDTO.getPhone())
                .set(coachUpdateDTO.getGender() != null,Coach::getGender, coachUpdateDTO.getGender())
                .set(coachUpdateDTO.getSpecialty() != null,Coach::getSpecialty, coachUpdateDTO.getSpecialty())
                .set(coachUpdateDTO.getCertification() != null,Coach::getCertification, coachUpdateDTO.getCertification())
                .set(coachUpdateDTO.getStatus() != null,Coach::getStatus, coachUpdateDTO.getStatus())
                .set(Coach::getUpdatedAt, LocalDateTime.now());

        int raw = coachMapper.update(wrapper);
        return raw == 1 ? Result.success("更新教练信息成功") : Result.error("更新教练信息失败");
    }
}
