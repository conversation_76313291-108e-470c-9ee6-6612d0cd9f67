server:
  port: 8888

spring:
  profiles:
    active: dev
  main:
    allow-circular-references: true
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
  h2:
    console:
      enabled: true
  sql:
    init:
      mode: always
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
#  jackson:
#    date-format:  yyyy-MM-dd HH:mm:ss
#    time-zone: GMT+8
gymguy:
  jwt:
    # 设置jwt签名加密时使用的秘钥
    employee-secret-key: haha
    # 设置jwt过期时间
    employee-ttl: 7200000
    # 设置前端传递过来的令牌名称
    employee-token-name: token
  alioss:
    endpoint: ${gymguy.alioss.endpoint}
    access-key-id: ${gymguy.alioss.access-key-id}
    access-key-secret: ${gymguy.alioss.access-key-secret}
    bucket-name: ${gymguy.alioss.bucket-name}

