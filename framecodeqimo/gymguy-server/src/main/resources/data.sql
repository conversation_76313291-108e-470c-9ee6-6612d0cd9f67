-- 健身房管理系统初始化数据
-- 插入管理员账号和测试数据

-- 插入管理员账号 (用户名: admin, 密码: 123456)
MERGE INTO employee (staff_id, name, gender, phone, role, hire_date, salary, status, username, password, created_time, avatar)
KEY(staff_id) VALUES (
    'admin-001',
    '系统管理员',
    '男',
    '13800138000',
    'admin',
    '2025-01-01',
    8000.00,
    1,
    'admin',
    'e10adc3949ba59abbe56e057f20f883e', -- 123456的MD5加密
    CURRENT_TIMESTAMP,
    NULL
);

-- 插入测试前台员工账号 (用户名: staff, 密码: 123456)
MERGE INTO employee (staff_id, name, gender, phone, role, hire_date, salary, status, username, password, created_time, avatar)
KEY(staff_id) VALUES (
    'staff-001',
    '前台员工',
    '女',
    '13800138001',
    'staff',
    '2025-01-01',
    4000.00,
    1,
    'staff',
    'e10adc3949ba59abbe56e057f20f883e', -- 123456的MD5加密
    CURRENT_TIMESTAMP,
    NULL
);

-- 插入测试教练账号 (用户名: coach, 密码: 123456)
MERGE INTO employee (staff_id, name, gender, phone, role, hire_date, salary, status, username, password, created_time, avatar)
KEY(staff_id) VALUES (
    'coach-001',
    '健身教练',
    '男',
    '13800138002',
    'coach',
    '2025-01-01',
    6000.00,
    1,
    'coach',
    'e10adc3949ba59abbe56e057f20f883e', -- 123456的MD5加密
    CURRENT_TIMESTAMP,
    NULL
);

-- 插入测试会员账号 (用户名: member, 密码: 123456)
MERGE INTO employee (staff_id, name, gender, phone, role, hire_date, salary, status, username, password, created_time, avatar)
KEY(staff_id) VALUES (
    'member-001',
    '普通会员',
    '女',
    '13800138003',
    'member',
    '2025-01-01',
    0.00,
    1,
    'member',
    'e10adc3949ba59abbe56e057f20f883e', -- 123456的MD5加密
    CURRENT_TIMESTAMP,
    NULL
);

-- 插入测试教练数据
INSERT INTO coach (name, gender, phone, speciality, experience, hourly_rate, status, created_time, avatar) 
VALUES 
    ('张教练', '男', '13900139000', '力量训练,健身指导', 5, 150.00, 1, CURRENT_TIMESTAMP, NULL),
    ('李教练', '女', '13900139001', '瑜伽,普拉提', 3, 120.00, 1, CURRENT_TIMESTAMP, NULL),
    ('王教练', '男', '13900139002', '有氧运动,减脂训练', 4, 130.00, 1, CURRENT_TIMESTAMP, NULL);

-- 插入测试课程数据
INSERT INTO course (name, description, coach_id, duration, max_capacity, price, status, created_time) 
VALUES 
    ('力量训练基础课', '适合初学者的力量训练课程', 1, 60, 10, 80.00, 1, CURRENT_TIMESTAMP),
    ('瑜伽入门课', '放松身心的瑜伽课程', 2, 90, 15, 60.00, 1, CURRENT_TIMESTAMP),
    ('有氧燃脂课', '高效燃脂的有氧运动课程', 3, 45, 20, 50.00, 1, CURRENT_TIMESTAMP);

-- 插入测试会员数据
INSERT INTO member (member_id, name, gender, phone, birthday, email, address, created_time, status) 
VALUES 
    ('member-001', '测试会员1', '男', '13700137000', '1990-01-01', '<EMAIL>', '北京市朝阳区', CURRENT_TIMESTAMP, 1),
    ('member-002', '测试会员2', '女', '13700137001', '1992-05-15', '<EMAIL>', '北京市海淀区', CURRENT_TIMESTAMP, 1);

-- 插入测试器材数据
INSERT INTO equipment (name, type, brand, model, purchase_date, price, status, location, created_time)
VALUES
    ('跑步机', '有氧器械', '美国品牌', 'TM-2000', '2024-01-01', 15000.00, 1, '有氧区A1', CURRENT_TIMESTAMP),
    ('哑铃组', '力量器械', '国产品牌', 'DB-SET-50', '2024-01-01', 3000.00, 1, '力量区B1', CURRENT_TIMESTAMP),
    ('动感单车', '有氧器械', '欧洲品牌', 'SC-PRO', '2024-01-01', 8000.00, 1, '有氧区A2', CURRENT_TIMESTAMP);

-- 插入测试公告数据
INSERT INTO notice (title, content, type, publisher_id, publisher_name, status, is_top, created_time, updated_time)
VALUES
    ('健身房开业通知', '欢迎大家来到我们的健身房！我们提供专业的健身设备和优质的服务。', 1, 'admin-001', '系统管理员', 1, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('春季健身活动', '春季健身活动即将开始，参与活动可获得精美礼品！', 2, 'admin-001', '系统管理员', 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
    ('设备维护通知', '本周末将对部分设备进行维护，请大家合理安排健身时间。', 3, 'admin-001', '系统管理员', 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);




