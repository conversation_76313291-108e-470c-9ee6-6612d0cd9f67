-- 健身房管理系统数据库表结构
-- H2数据库初始化脚本

-- 员工信息表
CREATE TABLE IF NOT EXISTS employee (
    staff_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '员工姓名',
    gender VARCHAR(10) COMMENT '员工性别',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    role VARCHAR(20) COMMENT '角色: admin-管理员,staff-前台,coach-教练,member-会员',
    hire_date DATE COMMENT '入职日期',
    salary DECIMAL(10,2) COMMENT '薪资',
    status INTEGER DEFAULT 1 COMMENT '状态:1-有效,2-过期,3-冻结',
    username VARCHAR(50) UNIQUE COMMENT '用户名',
    password VARCHAR(100) COMMENT '密码',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    avatar VARCHAR(200) COMMENT '头像'
);

-- 会员基础信息表
CREATE TABLE IF NOT EXISTS member (
    member_id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '会员姓名',
    gender VARCHAR(10) COMMENT '会员性别',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    birthday DATE COMMENT '生日',
    email VARCHAR(100) COMMENT '邮箱',
    address VARCHAR(200) COMMENT '地址',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    status INTEGER DEFAULT 1 COMMENT '状态:1-有效,2-过期,3-冻结'
);

-- 教练信息表
CREATE TABLE IF NOT EXISTS coach (
    coach_id INTEGER AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '教练姓名',
    gender VARCHAR(10) COMMENT '性别',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    speciality VARCHAR(100) COMMENT '专业特长',
    experience INTEGER COMMENT '工作经验(年)',
    hourly_rate DECIMAL(8,2) COMMENT '课时费',
    status INTEGER DEFAULT 1 COMMENT '状态:1-有效,2-过期,3-冻结',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    avatar VARCHAR(200) COMMENT '头像'
);

-- 课程信息表
CREATE TABLE IF NOT EXISTS course (
    course_id INTEGER AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '课程名称',
    description TEXT COMMENT '课程描述',
    coach_id INTEGER COMMENT '教练ID',
    duration INTEGER COMMENT '课程时长(分钟)',
    max_capacity INTEGER COMMENT '最大容量',
    price DECIMAL(8,2) COMMENT '课程价格',
    status INTEGER DEFAULT 1 COMMENT '状态:1-有效,2-过期,3-冻结',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (coach_id) REFERENCES coach(coach_id)
);

-- 会员卡信息表
CREATE TABLE IF NOT EXISTS member_card (
    card_id INTEGER AUTO_INCREMENT PRIMARY KEY,
    member_id VARCHAR(36) COMMENT '会员ID',
    card_type VARCHAR(50) COMMENT '卡类型',
    start_date DATE COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    remaining_times INTEGER COMMENT '剩余次数',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    status INTEGER DEFAULT 1 COMMENT '状态:1-有效,2-过期,3-冻结',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (member_id) REFERENCES member(member_id)
);

-- 课程预约表
CREATE TABLE IF NOT EXISTS booking (
    id INTEGER AUTO_INCREMENT PRIMARY KEY,
    member_id INTEGER COMMENT '学员ID',
    course_id INTEGER COMMENT '课程ID',
    booking_time TIMESTAMP COMMENT '预约时间',
    status INTEGER DEFAULT 1 COMMENT '状态:1=已预约,2=已出席,0=已取消,3=未到',
    checkin_time TIMESTAMP COMMENT '签到时间'
);

-- 器材信息表
CREATE TABLE IF NOT EXISTS equipment (
    equipment_id INTEGER AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '器材名称',
    type VARCHAR(50) COMMENT '器材类型',
    brand VARCHAR(50) COMMENT '品牌',
    model VARCHAR(50) COMMENT '型号',
    purchase_date DATE COMMENT '购买日期',
    price DECIMAL(10,2) COMMENT '价格',
    status INTEGER DEFAULT 1 COMMENT '状态:1-正常,2-维修中,3-报废',
    location VARCHAR(100) COMMENT '位置',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);

-- 公告信息表
CREATE TABLE IF NOT EXISTS notice (
    notice_id INTEGER AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL COMMENT '公告标题',
    content TEXT COMMENT '公告内容',
    type INTEGER DEFAULT 1 COMMENT '公告类型:1-系统公告,2-活动公告,3-维护公告',
    publisher_id VARCHAR(36) COMMENT '发布人ID',
    publisher_name VARCHAR(50) COMMENT '发布人姓名',
    status INTEGER DEFAULT 1 COMMENT '状态:1-发布,2-草稿,3-下线',
    is_top INTEGER DEFAULT 0 COMMENT '是否置顶:0-否,1-是',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
);
