// 权限控制工具函数

/**
 * 获取当前用户信息
 */
export function getCurrentUser() {
  const userInfo = localStorage.getItem('userInfo');
  return userInfo ? JSON.parse(userInfo) : null;
}

/**
 * 获取当前用户角色
 */
export function getCurrentUserRole() {
  const user = getCurrentUser();
  return user ? user.role : null;
}

/**
 * 检查用户是否有指定角色
 */
export function hasRole(role) {
  const currentRole = getCurrentUserRole();
  return currentRole === role;
}

/**
 * 检查用户是否有指定角色中的任意一个
 */
export function hasAnyRole(roles) {
  const currentRole = getCurrentUserRole();
  return roles.includes(currentRole);
}

/**
 * 检查是否是管理员
 */
export function isAdmin() {
  return hasRole('admin');
}

/**
 * 检查是否是前台员工
 */
export function isStaff() {
  return hasRole('staff');
}

/**
 * 检查是否是教练
 */
export function isCoach() {
  return hasRole('coach');
}

/**
 * 检查是否是会员
 */
export function isMember() {
  return hasRole('member');
}

/**
 * 根据角色获取可访问的菜单
 */
export function getMenusByRole() {
  const role = getCurrentUserRole();
  
  // 基础菜单（所有角色都可以访问）
  const baseMenus = [
    {
      path: "/home",
      name: "home",
      label: "首页",
      icon: "s-home"
    }
  ];

  // 根据角色返回不同的菜单
  switch (role) {
    case 'admin':
      // 管理员可以访问所有菜单
      return [
        ...baseMenus,
        {
          path: "/member",
          name: "member",
          label: "会员管理",
          icon: "user-solid"
        },
        {
          path: "/employee",
          name: "employee",
          label: "员工管理",
          icon: "user-solid"
        },
        {
          path: "/course",
          name: "course",
          label: "课程管理",
          icon: "user-solid"
        },
        {
          path: "/equipment",
          name: "equipment",
          label: "器材管理",
          icon: "user-solid"
        },
        {
          path: "/notice",
          name: "notice",
          label: "公告管理",
          icon: "user-solid"
        }
      ];
    
    case 'staff':
      // 前台员工可以访问会员管理、课程管理、器材管理、公告管理
      return [
        ...baseMenus,
        {
          path: "/member",
          name: "member",
          label: "会员管理",
          icon: "user-solid"
        },
        {
          path: "/course",
          name: "course",
          label: "课程管理",
          icon: "user-solid"
        },
        {
          path: "/equipment",
          name: "equipment",
          label: "器材管理",
          icon: "user-solid"
        },
        {
          path: "/notice",
          name: "notice",
          label: "公告管理",
          icon: "user-solid"
        }
      ];
    
    case 'coach':
      // 教练可以访问课程管理、会员管理
      return [
        ...baseMenus,
        {
          path: "/member",
          name: "member",
          label: "会员管理",
          icon: "user-solid"
        },
        {
          path: "/course",
          name: "course",
          label: "课程管理",
          icon: "user-solid"
        }
      ];
    
    case 'member':
      // 会员只能访问首页和课程查看
      return [
        ...baseMenus,
        {
          path: "/course",
          name: "course",
          label: "课程查看",
          icon: "user-solid"
        }
      ];
    
    default:
      // 未登录或未知角色只能访问首页
      return baseMenus;
  }
}

/**
 * 检查用户是否有权限访问指定路径
 */
export function hasPermissionToPath(path) {
  const menus = getMenusByRole();
  return menus.some(menu => menu.path === path);
}
