import axios from "axios";

const http = axios.create({
  baseURL: '/api',
  timeout: 3000,
})
// 添加请求拦截器
http.interceptors.request.use(
  config => {
    if(localStorage.getItem('token')){
      config.headers.token=localStorage.getItem('token')
    } 
    config.headers['X-Requested-With'] = 'XMLHttpRequest'
    return config;
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error);
  });

// 添加响应拦截器
// http.interceptors.response.use(
//   res => {
//     res.status === 200 ? Promise.resolve(res) : Promise.reject(res)
//   },
//   error => {
//     return Promise.reject(error);
//   });
export default http