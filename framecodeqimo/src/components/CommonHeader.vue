<template>
  <div class="header-container">
    <div class="l-content">
      <el-button
        type="info"
        class="l-button"
        @click="changeMenu"
        icon="el-icon-s-fold"
        style="font-size: 16px;"
        size="mini"
      ></el-button>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="tab in tabsList" :key="tab.name">{{ tab.label }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="r-content">
      <span class="user-info">
        <span class="user-name">{{ currentUser ? currentUser.name : '未登录' }}</span>
        <span class="user-role">{{ roleText }}</span>
      </span>
      <el-dropdown>
        <span class="el-dropdown-link">
          <img class="userAva" src="../assets/images/b_579.jpg" />
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleProfile">个人中心</el-dropdown-item>
          <el-dropdown-item @click.native="handleLogout">退出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { getCurrentUser, getCurrentUserRole } from '@/utils/permission';

export default {
  methods: {
    changeMenu() {
      this.$store.commit("tab/setCollapse");
    },
    // 个人中心
    handleProfile() {
      this.$message.info("个人中心功能开发中...");
    },
    // 退出登录
    handleLogout() {
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除本地存储的token和用户信息
        localStorage.removeItem('token');
        localStorage.removeItem('userInfo');
        // 清除vuex中的状态
        this.$store.commit('tab/resetTabs');
        // 跳转到登录页
        this.$router.push('/login');
        this.$message.success('退出登录成功');
      }).catch(() => {
        // 用户取消退出
      });
    }
  },
  computed:{
    ...mapState({
      tabsList: state => state.tab.tabsList
    }),
    currentUser() {
      return getCurrentUser();
    },
    currentUserRole() {
      return getCurrentUserRole();
    },
    roleText() {
      const roleMap = {
        'admin': '管理员',
        'coach': '教练',
        'staff': '前台',
        'member': '会员'
      };
      return roleMap[this.currentUserRole] || this.currentUserRole;
    }
  },
  mounted(){
    
  }
};
</script>
<style lang="less" scoped>
.header-container {
  background-color: #333;
  height: 60px;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .l-content {
    display: flex;
    align-items: center;
    .crumb {
      color: #fff;
      font-size: 14px;
      margin-left: 10px;
    }
    /deep/.el-breadcrumb__item{
      &:first-child{
        .el-breadcrumb__inner{
          color: #fff;
        }
      }
      .el-breadcrumb__inner{
        color: #97a8be;
      }
    }
    .l-button{
      margin-right: 20px;
    }
  }
  .r-content {
    display: flex;
    align-items: center;
    .user-info {
      margin-right: 15px;
      color: #fff;
      .user-name {
        font-size: 14px;
        margin-right: 8px;
      }
      .user-role {
        font-size: 12px;
        color: #97a8be;
        background: rgba(255, 255, 255, 0.1);
        padding: 2px 6px;
        border-radius: 3px;
      }
    }
    .userAva {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
  }
}
</style>