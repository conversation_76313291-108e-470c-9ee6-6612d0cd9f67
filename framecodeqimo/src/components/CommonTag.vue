<template>
  <div class="tags">
    <el-tag class="myTag"
        v-for="tag in tags" 
        :key="tag.name" 
        :closable="tag.name!=='home'"
        :effect="isHightLight(tag)"
        @click="changeMenu(tag)"
        @close="handleClose(tag)">
        {{tag.label}}
    </el-tag>
  </div>
</template>
<script>
import { mapState} from 'vuex';
export default{
    data(){
        return{

        }
    },
    computed:{
        ...mapState({
            tags: state=> state.tab.tags
        })
    },
    methods:{
        isHightLight(tag){
            return this.$route.path === tag.path ? 'dark':'plain'
        },
        changeMenu(tag){
            this.$router.push(tag.path)
            this.$store.commit("tab/setTabsList",tag)
        },
        // 删除不是当前选中的，直接删，是当前选中的删除后路由切换到最后一个标签
        handleClose(tag){
            this.$store.commit("tab/deleteTag",tag)
            if(tag.path===this.$route.path){
                //console.log("删除的是当前选中的")
                const length=this.tags.length-1
                this.$router.push(this.tags[length].path)
            }
        }
    }
}
</script>
<style lang="less" scoped>
.tags{
    padding: 6px 0 6px 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}
.myTag{
    cursor: pointer;
    margin-right: 6px;
}
</style>