<template>
  <div>
    <el-container style="height: 100vh;">
      <el-aside width="auto">
        <common-aside/>
      </el-aside>
      <el-container>
        <el-header>
          <common-header/>
        </el-header>
        <common-tag/>
        <el-main style="overflow-y: auto;">
          <router-view></router-view>
        </el-main>
      </el-container>
    </el-container>    
  </div>
</template>
<script>
import CommonAside from "@/components/CommonAside.vue";
import CommonHeader from "@/components/CommonHeader.vue";
import CommonTag from "@/components/CommonTag.vue";
export default{
  components:{CommonAside,CommonHeader,CommonTag}
}
</script>
<style class="less">
.el-aside{
  overflow: hidden;
}
.el-header{
  padding: 0;
}
</style>