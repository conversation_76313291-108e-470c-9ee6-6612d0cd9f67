<template>
  <div style="">
    <!-- 页面标题 -->
    <!-- <el-header class="page-header">
      <h2>数据概览</h2>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item>首页</el-breadcrumb-item>
        <el-breadcrumb-item>系统概览</el-breadcrumb-item>
      </el-breadcrumb>
    </el-header> -->
    <!-- 主要区域 -->
    <el-row style="margin-top:10px" :gutter="20">
      <!-- 左侧区域 -->
      <el-col :span="7">
        <el-card class="upcard">
          <div class="user">
            <img src="../assets/images/b_579.jpg" alt />
            <div class="userinfo">
              <p class="name">{{ user.name }}</p>
              <p class="role">{{user.role}}</p>
            </div>
          </div>
          <div class="info">
            <p>
              上次登录时间:
              <span>2025-6-6</span>
            </p>
            <p>
              上次登录地点:
              <span>成都</span>
            </p>
          </div>
        </el-card>
        <el-card class="downcard">
          <el-table :data="tableData" style="width: 100%">
          </el-table>
        </el-card>
      </el-col>
      <!-- 右侧区域 -->
      <el-col :span="17" class="container">
        <!-- 数据概览卡片 -->
        <el-row :gutter="20" class="stats-row">
          <el-col :span="6">
            <el-card class="stats-card">
              <div class="stats-content">
                <div class="stats-title">总会员数</div>
                <div class="stats-value">{{ totalMembers }}</div>
                <div class="stats-trend">
                  <i class="el-icon-caret-top"></i>
                  <span>12.5%</span>
                  <span class="stats-time">较上月</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stats-card">
              <div class="stats-content">
                <div class="stats-title">今日收入</div>
                <div class="stats-value">¥{{ todayIncome }}</div>
                <div class="stats-trend">
                  <i class="el-icon-caret-top"></i>
                  <span>8.2%</span>
                  <span class="stats-time">较昨日</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stats-card">
              <div class="stats-content">
                <div class="stats-title">今日课程</div>
                <div class="stats-value">{{ todayCourses }}</div>
                <div class="stats-trend">
                  <i class="el-icon-caret-bottom"></i>
                  <span>3.1%</span>
                  <span class="stats-time">较昨日</span>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stats-card">
              <div class="stats-content">
                <div class="stats-title">活跃设备</div>
                <div class="stats-value">{{ activeDevices }}</div>
                <div class="stats-trend">
                  <i class="el-icon-caret-top"></i>
                  <span>5.7%</span>
                  <span class="stats-time">较昨日</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 数据图表区 -->
        <el-row :gutter="20" class="chart-row">
          <el-col :span="16">
            <el-card class="chart-card">
              <div class="chart-title">会员增长趋势</div>
              <div class="chart-container" ref="membersChart"></div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="chart-card">
              <div class="chart-title">课程类型分布</div>
              <div class="chart-container" ref="coursesChart"></div>
            </el-card>
          </el-col>
        </el-row>
        <!-- 公告和提醒区 -->
        <el-row :gutter="20" class="notice-row">
          <el-col :span="12">
            <el-card class="notice-card">
              <div class="card-header">
                <div class="card-title">系统公告</div>
                <el-button size="mini" type="text">更多</el-button>
              </div>
              <div :data="notices" class="notice-list" style="margin-bottom: 0">
                  <div v-for="(item, index) in notices" :key="index" class="notice-item">
                    <div class="notice-content">
                      <span class="notice-title">{{ item.title }}</span>
                      <span class="notice-time">{{ item.time }}</span>
                    </div>
                  </div>
                </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="notice-card">
              <div class="card-header">
                <div class="card-title">设备提醒</div>
                <el-button size="mini" type="text">更多</el-button>
              </div>
              <el-scrollbar wrap-class="scrollbar-wrapper">
                <div :data="deviceReminders" class="notice-list" style="margin-bottom: 0">
                  <div
                    v-for="(item, index) in deviceReminders"
                    :key="index"
                    class="notice-item"
                  >
                    <div class="notice-content">
                      <span class="notice-title">
                        <el-tag size="mini" :type="item.type === '维护' ? 'warning' : 'danger'">{{ item.type }}</el-tag>
                        {{ item.deviceName }}
                      </span>
                      <span class="notice-time">{{ item.time }}</span>
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row> 
  </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
  data() {
    return {
      user: {
        name: "Admin",
        role: "管理员"
      },
      tableData: [],
      totalMembers: 4656,
      todayCourses: 12,
      activeDevices: 23,
      todayIncome: 2366,
      notices: [
        { title: '夏季特惠活动开始啦！会员续卡享8折优惠', time: '2025-06-06' },
        { title: '新设备已安装完毕，欢迎体验', time: '2025-06-05' },
        { title: '本周六上午10点将举办会员交流活动', time: '2025-06-04' },
        { title: '系统将于6月8日22:00-24:00进行维护', time: '2025-06-03' },
        { title: '夏季特惠活动开始啦！会员续卡享8折优惠', time: '2025-06-06' },
        { title: '新设备已安装完毕，欢迎体验', time: '2025-06-05' },
      ],
      deviceReminders: [
        { deviceName: '跑步机01', type: '维护', time: '2025-06-07' },
        { deviceName: '椭圆机03', type: '维护', time: '2025-06-08' },
        { deviceName: '哑铃架A', type: '故障', time: '2025-06-06' },
        { deviceName: '跑步机01', type: '维护', time: '2025-06-07' },
        { deviceName: '椭圆机03', type: '维护', time: '2025-06-08' },
        { deviceName: '哑铃架A', type: '故障', time: '2025-06-06' },
      ],
    };
  },
  methods:{
    initData(){
      // 获取首页数据
      
    },
    // 初始化图表
    initCharts() {
      // 会员增长趋势图
      this.membersChart = echarts.init(this.$refs.membersChart);
      const membersOption = {
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '新增会员',
          data: [120, 190, 170, 210, 230, 250],
          type: 'line',
          smooth: true,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: 'rgba(64, 158, 255, 0.1)'
          }
        }]
      };
      this.membersChart.setOption(membersOption);

      // 课程类型分布图
      this.coursesChart = echarts.init(this.$refs.coursesChart);
      const coursesOption = {
        tooltip: {
          trigger: 'item'
        },
        series: [{
          name: '课程类型',
          type: 'pie',
          radius: ['40%', '70%'],
          data: [
            { value: 30, name: '瑜伽' },
            { value: 25, name: '动感单车' },
            { value: 20, name: '力量训练' },
            { value: 15, name: '舞蹈' },
            { value: 10, name: '其他' }
          ],
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          }
        }]
      };
      this.coursesChart.setOption(coursesOption);
    }
  },
  mounted(){
    this.initData()

    this.initCharts()
  }
};
</script>
<style lang="less" scoped>
.page-header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}
.downcard {
  margin-top: 30px;
  height: 460px;
}
.user {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #ccc;
  margin-bottom: 20px;
  img {
    height: 150px;
    width: 150px;
    border-radius: 50%;
  }
  .userinfo {
    margin-left: 50px;
    .name {
      font-size: 32px;
    }
    .role {
      color: #999999;
    }
  }
}
.info {
  p {
    line-height: 28px;
    font-size: 14px;
    color: #999999;
    span {
      color: #666666;
      margin-left: 60px;
    }
  }
}
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;

  .stats-row {
    margin-top: 0px;
    padding: 0 0;
    .stats-card {
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .stats-content {
        text-align: center;
        .stats-title {
          font-size: 14px;
          color: #909399;
          margin-bottom: 10px;
        }

        .stats-value {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 10px;
        }

        .stats-trend {
          font-size: 12px;
          color: #67c23a;
        }
      }
    }
  }
  .chart-row {
    margin-top: 20px;
    .chart-card {
      height: 300px;
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .chart-container {
        height: 250px;
      }
    }
  }
  .notice-row {
    margin-top: 20px;
    .notice-card {
      height: 315px;
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .card-title {
          font-size: 16px;
          font-weight: bold;
        }
      }
      .notice-list {
        padding: 0;
        .notice-item {
          padding: 10px 0;
          // border-bottom: 1px solid #ebeef5;
          .notice-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .notice-title {
              font-size: 14px;
              color: #303133;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 80%;
            }

            .notice-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
    }
  }
}
</style>