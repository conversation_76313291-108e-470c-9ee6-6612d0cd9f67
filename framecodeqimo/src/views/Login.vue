<template>
  <div class="login-container">
    <div class="login-box">
      <h2 class="login-title">Fit健身房管理系统</h2>
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm" class="login-form">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名" prefix-icon="el-icon-user"></el-input>
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
            prefix-icon="el-icon-lock"
            @keyup.enter.native="handleLogin"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" class="login-btn" :loading="loading" @click="handleLogin">登录</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { login } from "@/api/user";
export default {
  data() {
    return {
      loginForm: {
        username: "",
        password: ""
      },
      loginRules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          { min: 3, max: 20, message: "长度在 3 到 20 个字符", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
        ]
      },
      loading: false
    };
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          // 这里替换为实际的登录API调用
          login(this.loginForm).then(res => {
            if (res.data.code === 1) {
              const loginData = res.data.data;
              const token = loginData.employeeToken;
              const userInfo = {
                name: loginData.name,
                role: loginData.role,
                avatar: loginData.avatar
              };

              // 存储token和用户信息
              localStorage.setItem('token', token);
              localStorage.setItem('userInfo', JSON.stringify(userInfo));

              this.$message.success("登录成功");
              this.$router.push("/home");
            }else{
                const msg=res.data.msg
                this.$message.error(msg)
                this.loading=false
            }
          });
        } else {
          return false;
        }
      });
    }
  }
};
</script>
  
<style scoped>
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #1e5799 0%, #207cca 51%, #2989d8 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-title {
  margin-bottom: 30px;
  text-align: center;
  color: #333;
  font-size: 24px;
}

.login-form {
  margin-top: 20px;
}

.login-btn {
  width: 100%;
  margin-top: 10px;
}
</style>