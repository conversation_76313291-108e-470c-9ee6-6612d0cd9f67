const state = {
    isCollapse: false,
    tabsList: [
        {
            path: "/home",
            name: "home",
            label: "首页",
            icon: "s-home"
        }
    ],
    tags: [
        {
            path: "/home",
            name: "home",
            label: "首页",
            icon: "s-home"
        }
    ]
}

const mutations = {
    setCollapse(state) {
        state.isCollapse = !state.isCollapse
    },
    // 更新面包屑数据
    setTabsList(state, val) {
        //console.log(val)
        // 首页+当前选择菜单
        const tabs = [
            {
                path: "/",
                name: "home",
                label: "首页",
                icon: "s-home"
            }
        ]
        if (!Array.isArray(val)) {
            if (val.name !== 'home') {
                tabs.push(val)
                // 添加到tags
                const index = state.tags.findIndex(item => item.name === val.name)
                if (index === -1) {
                    state.tags.push(val)
                }
            }
        } else {
            for (const item of val) {
                tabs.push(item)
            }
            //子菜单添加(包含父级)
            // const newParent=JSON.parse(JSON.stringify(val[0]))
            // newParent.children=val[1]
            //console.log(newParent)
            const index = state.tags.findIndex(item => item.name === val[1].name)
            if (index === -1) {
                state.tags.push(val[1])
            }
        }
        state.tabsList = tabs
    },
    deleteTag(state,val){
        //console.log(val)
        const index=state.tags.findIndex(item=>item.name===val.name)
        state.tags.splice(index,1)
    },
    // 重置标签用于退出登录
    resetTabs(state) {
        state.tabsList = [
            {
                path: "/home",
                name: "home",
                label: "首页",
                icon: "s-home"
            }
        ];
        state.tags = [
            {
                path: "/home",
                name: "home",
                label: "首页",
                icon: "s-home"
            }
        ];
        state.isCollapse = false;
    }
}

const getters = {
    getTags(state){
        const index=state.tags.findIndex(item=>item.children)
        state.tags[index]
        state.tags.splice(index,1)
        return state.tags
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    getters
}