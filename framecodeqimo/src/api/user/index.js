import http from '@/utils/request'

export function queryEmployees(params){
    return http.post('admin/employee/list',{...params})
}

export function saveEmployee(params){
    return http.post('admin/employee/add',{...params})
}

export function updateEmployee(params){
    return http.put('admin/employee/update',{...params})
}

export function deleteEmployee(id){
    return http.delete(`admin/employee/delete/${id}`)
}

export function resetEmployeePassword(params){
    return http.put('admin/employee/resetPassword',{...params})
}

export function login(params){
    return http.post('/admin/employee/login',{...params})
}