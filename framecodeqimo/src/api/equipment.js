import request from '@/utils/request'

// 查询器材列表
export function queryEquipments(params) {
  return request({
    url: '/admin/equipment/page',
    method: 'get',
    params
  })
}

// 添加器材
export function saveEquipment(data) {
  return request({
    url: '/admin/equipment',
    method: 'post',
    data
  })
}

// 更新器材
export function updateEquipment(data) {
  return request({
    url: '/admin/equipment',
    method: 'put',
    data
  })
}

// 删除器材
export function deleteEquipment(id) {
  return request({
    url: `/admin/equipment/${id}`,
    method: 'delete'
  })
}

// 根据ID查询器材
export function getEquipmentById(id) {
  return request({
    url: `/admin/equipment/${id}`,
    method: 'get'
  })
}

// 更新器材状态
export function updateEquipmentStatus(id, status) {
  return request({
    url: `/admin/equipment/${id}/status/${status}`,
    method: 'put'
  })
}
