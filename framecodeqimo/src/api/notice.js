import request from '@/utils/request'

// 查询公告列表
export function queryNotices(params) {
  return request({
    url: '/admin/notice/page',
    method: 'get',
    params
  })
}

// 添加公告
export function saveNotice(data) {
  return request({
    url: '/admin/notice',
    method: 'post',
    data
  })
}

// 更新公告
export function updateNotice(data) {
  return request({
    url: '/admin/notice',
    method: 'put',
    data
  })
}

// 删除公告
export function deleteNotice(id) {
  return request({
    url: `/admin/notice/${id}`,
    method: 'delete'
  })
}

// 根据ID查询公告
export function getNoticeById(id) {
  return request({
    url: `/admin/notice/${id}`,
    method: 'get'
  })
}

// 更新公告状态
export function updateNoticeStatus(id, status) {
  return request({
    url: `/admin/notice/${id}/status/${status}`,
    method: 'put'
  })
}

// 更新公告置顶状态
export function updateNoticeTop(id, isTop) {
  return request({
    url: `/admin/notice/${id}/top/${isTop}`,
    method: 'put'
  })
}
