package com.gymguy.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description AliOss配置属性
 * @Date 2025/6/13 10:15
 */
@Component
@ConfigurationProperties(prefix = "gymguy.alioss")//从配置文件中前缀为sky.alioss的配置读取进行注入
@Data
public class AliOssProperties {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
}
