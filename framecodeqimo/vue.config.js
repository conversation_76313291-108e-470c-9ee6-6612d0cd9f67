const { defineConfig } = require('@vue/cli-service')

const port = 9527 // dev port

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false, //关闭eslint校验
  devServer: {
    port:port,
    client:{
      overlay:{
        warnings: false,
        errors: true,
        runtimeErrors: false
      }
    },
    proxy:{
      '/api':{
        target: 'http://localhost:8888/',
        changeOrigin: true,
        pathRewrite:{
          // 替换target中的请求地址，也就是说/api=/target，请求target这个地址的时候直接写成/api即可
          '^/api': ''
        }
      }
    }
  }
})
